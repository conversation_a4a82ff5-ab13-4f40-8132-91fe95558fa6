using UnityEngine;

[System.Serializable]
public enum Season
{
    Spring,  // İlkbahar
    Summer,  // Yaz
    Autumn,  // Sonbahar
    Winter   // Kış
}

[CreateAssetMenu(fileName = "New Card", menuName = "HexHarvest/Card", order = 1)]
public class CardSO : ScriptableObject
{
    [Header("Card Information")]
    [SerializeField] private string cardName = "New Card";
    [SerializeField] private Sprite cardSprite;
    [SerializeField] private Season cardSeason = Season.Spring;
    [SerializeField] private GameObject cardPrefab;

    // Properties
    public string CardName
    {
        get { return cardName; }
        set { cardName = value; }
    }

    public Sprite CardSprite
    {
        get { return cardSprite; }
        set { cardSprite = value; }
    }

    public Season CardSeason
    {
        get { return cardSeason; }
        set { cardSeason = value; }
    }

    public GameObject CardPrefab
    {
        get { return cardPrefab; }
        set { cardPrefab = value; }
    }

    /// <summary>
    /// Mevsimin <PERSON> ismini döndürür
    /// </summary>
    /// <returns>Mevsim adı</returns>
    public string GetSeasonString()
    {
        switch (cardSeason)
        {
            case Season.Spring:
                return "İlkbahar";
            case Season.Summer:
                return "Yaz";
            case Season.Autumn:
                return "Sonbahar";
            case Season.Winter:
                return "Kış";
            default:
                return "Bilinmeyen";
        }
    }

    /// <summary>
    /// Mevsime göre renk döndürür
    /// </summary>
    /// <returns>Mevsim rengi</returns>
    public Color GetSeasonColor()
    {
        switch (cardSeason)
        {
            case Season.Spring:
                return Color.green;
            case Season.Summer:
                return Color.yellow;
            case Season.Autumn:
                return new Color(1f, 0.5f, 0f); // Turuncu
            case Season.Winter:
                return Color.cyan;
            default:
                return Color.white;
        }
    }
}

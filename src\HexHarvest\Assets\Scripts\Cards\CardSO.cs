using UnityEngine;

[CreateAssetMenu(fileName = "New Card", menuName = "HexHarvest/Card", order = 1)]
public class CardSO : ScriptableObject
{
    [Header("Card Information")]
    [SerializeField] private string cardName = "New Card";
    [SerializeField] private Sprite cardSprite;
    [SerializeField] private Season cardSeason = Season.Spring;
    [SerializeField] private GameObject cardPrefab;

    // Properties
    public string CardName
    {
        get { return cardName; }
        set { cardName = value; }
    }

    public Sprite CardSprite
    {
        get { return cardSprite; }
        set { cardSprite = value; }
    }

    public Season CardSeason
    {
        get { return cardSeason; }
        set { cardSeason = value; }
    }

    public GameObject CardPrefab
    {
        get { return cardPrefab; }
        set { cardPrefab = value; }
    }

    /// <summary>
    /// Kartın mevsiminin Türkçe ismini döndürür
    /// </summary>
    /// <returns>Mevsim adı</returns>
    public string GetSeasonString()
    {
        return Seasons.GetSeasonString(cardSeason);
    }

    /// <summary>
    /// Kartın mevsimine göre renk döndürür
    /// </summary>
    /// <returns>Mevsim rengi</returns>
    public Color GetSeasonColor()
    {
        return Seasons.GetSeasonColor(cardSeason);
    }
}

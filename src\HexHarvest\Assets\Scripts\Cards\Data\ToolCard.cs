using UnityEngine;

[CreateAssetMenu(fileName = "New Tool Card", menuName = "HexHarvest/Cards/Tool Card", order = 3)]
public class ToolCard : BaseCard
{
    [Header("Tool Specific")]
    [SerializeField] private ToolType toolType = ToolType.Farming;
    [SerializeField] private int efficiency = 1;
    [SerializeField] private int maxUses = 10;
    [SerializeField] private bool isRepairable = true;
    [SerializeField] private int repairCost = 1;
    
    [Header("Tool Effects")]
    [SerializeField] private float speedMultiplier = 1f;
    [SerializeField] private float qualityBonus = 0f;
    [SerializeField] private bool hasSpecialEffect = false;
    [SerializeField, TextArea(2, 3)] private string specialEffectDescription = "";

    public enum ToolType
    {
        Farming,    // Çiftçilik
        Harvesting, // Hasat
        Watering,   // Sulama
        Building,   // İnşaat
        Mining,     // Madencilik
        Crafting    // Üretim
    }

    // Properties
    public ToolType Type 
    { 
        get { return toolType; } 
        set { toolType = value; }
    }
    
    public int Efficiency 
    { 
        get { return efficiency; } 
        set { efficiency = Mathf.Max(1, value); }
    }
    
    public int MaxUses 
    { 
        get { return maxUses; } 
        set { maxUses = Mathf.Max(1, value); }
    }
    
    public bool IsRepairable 
    { 
        get { return isRepairable; } 
        set { isRepairable = value; }
    }
    
    public int RepairCost 
    { 
        get { return repairCost; } 
        set { repairCost = Mathf.Max(0, value); }
    }
    
    public float SpeedMultiplier 
    { 
        get { return speedMultiplier; } 
        set { speedMultiplier = Mathf.Max(0.1f, value); }
    }
    
    public float QualityBonus 
    { 
        get { return qualityBonus; } 
        set { qualityBonus = Mathf.Max(0f, value); }
    }
    
    public bool HasSpecialEffect 
    { 
        get { return hasSpecialEffect; } 
        set { hasSpecialEffect = value; }
    }
    
    public string SpecialEffectDescription 
    { 
        get { return specialEffectDescription; } 
        set { specialEffectDescription = value; }
    }

    /// <summary>
    /// Aracın kullanılabilir olup olmadığını kontrol eder
    /// </summary>
    /// <param name="currentUses">Mevcut kullanım sayısı</param>
    /// <returns>Kullanılabilir mi</returns>
    public bool CanUse(int currentUses = 0)
    {
        return IsPlayable && currentUses < maxUses;
    }

    /// <summary>
    /// Aracın tamir edilebilir olup olmadığını kontrol eder
    /// </summary>
    /// <param name="availableResources">Mevcut kaynaklar</param>
    /// <param name="currentUses">Mevcut kullanım sayısı</param>
    /// <returns>Tamir edilebilir mi</returns>
    public bool CanRepair(int availableResources, int currentUses)
    {
        return isRepairable && currentUses >= maxUses && availableResources >= repairCost;
    }

    /// <summary>
    /// Araç türüne göre Türkçe isim döndürür
    /// </summary>
    /// <returns>Araç türü adı</returns>
    public string GetToolTypeString()
    {
        switch (toolType)
        {
            case ToolType.Farming:
                return "Çiftçilik";
            case ToolType.Harvesting:
                return "Hasat";
            case ToolType.Watering:
                return "Sulama";
            case ToolType.Building:
                return "İnşaat";
            case ToolType.Mining:
                return "Madencilik";
            case ToolType.Crafting:
                return "Üretim";
            default:
                return "Bilinmeyen";
        }
    }

    /// <summary>
    /// Aracın etkinlik seviyesine göre renk döndürür
    /// </summary>
    /// <returns>Etkinlik rengi</returns>
    public Color GetEfficiencyColor()
    {
        if (efficiency >= 5)
            return Color.green;
        else if (efficiency >= 3)
            return Color.yellow;
        else
            return Color.red;
    }

    /// <summary>
    /// Araç kartı oynanırken çağrılır
    /// </summary>
    public override void OnPlay()
    {
        base.OnPlay();
        Debug.Log($"{CardName} kullanıma hazır! Etkinlik: {efficiency}");
    }

    /// <summary>
    /// Araç kullanıldığında çağrılır
    /// </summary>
    /// <param name="currentUses">Mevcut kullanım sayısı</param>
    /// <returns>Kullanım sonrası yeni kullanım sayısı</returns>
    public int OnUse(int currentUses)
    {
        if (!CanUse(currentUses))
        {
            Debug.LogWarning($"{CardName} kullanılamaz! Maksimum kullanım sayısına ulaşıldı.");
            return currentUses;
        }

        base.OnUse();
        int newUses = currentUses + 1;
        
        Debug.Log($"{CardName} kullanıldı! Kalan kullanım: {maxUses - newUses}");
        
        if (newUses >= maxUses)
        {
            Debug.Log($"{CardName} kullanım ömrünü tamamladı!");
        }
        
        return newUses;
    }

    /// <summary>
    /// Araç tamir edildiğinde çağrılır
    /// </summary>
    /// <returns>Tamir sonrası kullanım sayısı (0)</returns>
    public int OnRepair()
    {
        if (!isRepairable)
        {
            Debug.LogWarning($"{CardName} tamir edilemez!");
            return maxUses;
        }
        
        Debug.Log($"{CardName} tamir edildi! Tamir maliyeti: {repairCost}");
        return 0;
    }

    /// <summary>
    /// Özel efekt aktif edildiğinde çağrılır
    /// </summary>
    public virtual void ActivateSpecialEffect()
    {
        if (!hasSpecialEffect)
        {
            Debug.LogWarning($"{CardName} özel efekte sahip değil!");
            return;
        }
        
        Debug.Log($"{CardName} özel efekti aktif edildi: {specialEffectDescription}");
    }

    /// <summary>
    /// Araç kartını kopyalar
    /// </summary>
    /// <returns>Kopya kart</returns>
    public override BaseCard Clone()
    {
        ToolCard clone = CreateInstance<ToolCard>();
        
        // Base properties
        clone.CardName = this.CardName;
        clone.CardID = this.CardID;
        clone.Description = this.Description;
        clone.CardImage = this.CardImage;
        clone.CardIcon = this.CardIcon;
        clone.CardColor = this.CardColor;
        clone.Type = CardType.Tool;
        clone.Rarity = this.Rarity;
        clone.Cost = this.Cost;
        clone.IsPlayable = this.IsPlayable;
        clone.Value = this.Value;
        clone.Durability = this.Durability;
        clone.IsConsumable = this.IsConsumable;
        
        // Tool specific properties
        clone.toolType = this.toolType;
        clone.efficiency = this.efficiency;
        clone.maxUses = this.maxUses;
        clone.isRepairable = this.isRepairable;
        clone.repairCost = this.repairCost;
        clone.speedMultiplier = this.speedMultiplier;
        clone.qualityBonus = this.qualityBonus;
        clone.hasSpecialEffect = this.hasSpecialEffect;
        clone.specialEffectDescription = this.specialEffectDescription;
        
        return clone;
    }

    public override string ToString()
    {
        return $"{CardName} ({GetToolTypeString()}) - Etkinlik: {efficiency}, Kullanım: {maxUses}";
    }

    private void OnValidate()
    {
        base.OnValidate();
        
        // Araç kartı için tip otomatik ayarla
        if (Type != CardType.Tool)
        {
            Type = CardType.Tool;
        }
        
        // Negatif değerleri düzelt
        efficiency = Mathf.Max(1, efficiency);
        maxUses = Mathf.Max(1, maxUses);
        repairCost = Mathf.Max(0, repairCost);
        speedMultiplier = Mathf.Max(0.1f, speedMultiplier);
        qualityBonus = Mathf.Max(0f, qualityBonus);
    }
}

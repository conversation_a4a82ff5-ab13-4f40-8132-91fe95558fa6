using UnityEngine;
using UnityEngine.UI;
using TMPro;

public class UIManager : MonoBehaviour
{
    [Header("Season UI")]
    [SerializeField] private Button readyButton;
    [SerializeField] private TextMeshProUGUI seasonText;
    
    [Header("UI Settings")]
    [SerializeField] private bool updateSeasonTextColor = true;
    [SerializeField] private string seasonTextPrefix = "Mevsim: ";
    
    // Singleton pattern
    public static UIManager Instance { get; private set; }

    private void Awake()
    {
        // Singleton setup
        if (Instance == null)
        {
            Instance = this;
        }
        else
        {
            Destroy(gameObject);
        }
    }

    private void Start()
    {
        // Button event'ini bağla
        if (readyButton != null)
        {
            readyButton.onClick.AddListener(OnReadyButtonClicked);
        }
        
        // GameManager event'ini dinle
        if (GameManager.Instance != null)
        {
            GameManager.Instance.OnSeasonChanged.AddListener(UpdateSeasonUI);
            // <PERSON>lk güncelleme
            UpdateSeasonUI(GameManager.Instance.CurrentSeason);
        }
        else
        {
            Debug.LogWarning("GameManager bulunamadı! UIManager düzgün çalışmayabilir.");
        }
    }

    private void OnDestroy()
    {
        // Event'leri temizle
        if (readyButton != null)
        {
            readyButton.onClick.RemoveListener(OnReadyButtonClicked);
        }
        
        if (GameManager.Instance != null)
        {
            GameManager.Instance.OnSeasonChanged.RemoveListener(UpdateSeasonUI);
        }
    }

    /// <summary>
    /// Ready butonu tıklandığında çağrılır
    /// </summary>
    private void OnReadyButtonClicked()
    {
        if (GameManager.Instance != null)
        {
            GameManager.Instance.NextSeason();
        }
        else
        {
            Debug.LogError("GameManager bulunamadı!");
        }
    }

    /// <summary>
    /// Mevsim UI'ını günceller
    /// </summary>
    /// <param name="newSeason">Yeni mevsim</param>
    private void UpdateSeasonUI(Season newSeason)
    {
        if (seasonText != null)
        {
            // Mevsim metnini güncelle
            string seasonName = Seasons.GetSeasonString(newSeason);
            seasonText.text = seasonTextPrefix + seasonName;
            
            // Mevsim rengini güncelle
            if (updateSeasonTextColor)
            {
                seasonText.color = Seasons.GetSeasonColor(newSeason);
            }
        }
        else
        {
            Debug.LogWarning("Season Text referansı atanmamış!");
        }
    }

    /// <summary>
    /// Ready butonunu aktif/pasif yapar
    /// </summary>
    /// <param name="isActive">Aktif mi</param>
    public void SetReadyButtonActive(bool isActive)
    {
        if (readyButton != null)
        {
            readyButton.interactable = isActive;
        }
    }

    /// <summary>
    /// Ready buton metnini değiştirir
    /// </summary>
    /// <param name="newText">Yeni metin</param>
    public void SetReadyButtonText(string newText)
    {
        if (readyButton != null)
        {
            TextMeshProUGUI buttonText = readyButton.GetComponentInChildren<TextMeshProUGUI>();
            if (buttonText != null)
            {
                buttonText.text = newText;
            }
        }
    }

    /// <summary>
    /// Mevsim metnini manuel olarak günceller
    /// </summary>
    /// <param name="customText">Özel metin</param>
    public void SetSeasonText(string customText)
    {
        if (seasonText != null)
        {
            seasonText.text = customText;
        }
    }

    /// <summary>
    /// Mevsim metninin rengini değiştirir
    /// </summary>
    /// <param name="color">Yeni renk</param>
    public void SetSeasonTextColor(Color color)
    {
        if (seasonText != null)
        {
            seasonText.color = color;
        }
    }

    /// <summary>
    /// UI'ı tamamen yeniler
    /// </summary>
    public void RefreshUI()
    {
        if (GameManager.Instance != null)
        {
            UpdateSeasonUI(GameManager.Instance.CurrentSeason);
        }
    }

    // Inspector'da test için
    [ContextMenu("Test - Next Season")]
    private void TestNextSeason()
    {
        OnReadyButtonClicked();
    }

    [ContextMenu("Test - Refresh UI")]
    private void TestRefreshUI()
    {
        RefreshUI();
    }
}

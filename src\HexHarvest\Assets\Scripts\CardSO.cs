using UnityEngine;

[CreateAssetMenu(fileName = "New Card", menuName = "HexHarvest/Card", order = 1)]
public class CardSO : ScriptableObject
{
    [Header("Card Information")]
    [SerializeField] private string cardName = "New Card";
    [SerializeField] private Sprite cardSprite;

    // Properties
    public string CardName 
    { 
        get { return cardName; } 
        set { cardName = value; }
    }
    
    public Sprite CardSprite 
    { 
        get { return cardSprite; } 
        set { cardSprite = value; }
    }
}

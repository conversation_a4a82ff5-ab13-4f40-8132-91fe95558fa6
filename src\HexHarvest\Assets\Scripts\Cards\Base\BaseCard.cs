using UnityEngine;

[System.Serializable]
public enum CardType
{
    None,
    Crop,       // <PERSON>kin kartları
    Tool,       // <PERSON><PERSON> kartları
    Action,     // <PERSON>ks<PERSON>yon kartları
    Special,    // Özel kartlar
    Resource    // Kaynak kartları
}

[System.Serializable]
public enum CardRarity
{
    Common,     // Yaygın
    Uncommon,   // Nadir
    Rare,       // Ender
    Epic,       // Epik
    Legendary   // Efsanevi
}

[CreateAssetMenu(fileName = "New Card", menuName = "HexHarvest/Cards/Base Card", order = 1)]
public class BaseCard : ScriptableObject
{
    [Header("Basic Information")]
    [SerializeField] private string cardName = "New Card";
    [SerializeField] private string cardID = "";
    [SerializeField, TextArea(3, 5)] private string description = "";
    
    [Header("Visual")]
    [SerializeField] private Sprite cardImage;
    [SerializeField] private Sprite cardIcon;
    [SerializeField] private Color cardColor = Color.white;
    
    [Header("Card Properties")]
    [SerializeField] private CardType cardType = CardType.None;
    [SerializeField] private CardRarity rarity = CardRarity.Common;
    [SerializeField] private int cost = 0;
    [SerializeField] private bool isPlayable = true;
    
    [Header("Game Values")]
    [SerializeField] private int value = 0;
    [SerializeField] private int durability = 1;
    [SerializeField] private bool isConsumable = false;

    // Properties
    public string CardName 
    { 
        get { return cardName; } 
        set { cardName = value; }
    }
    
    public string CardID 
    { 
        get { return string.IsNullOrEmpty(cardID) ? name : cardID; } 
        set { cardID = value; }
    }
    
    public string Description 
    { 
        get { return description; } 
        set { description = value; }
    }
    
    public Sprite CardImage 
    { 
        get { return cardImage; } 
        set { cardImage = value; }
    }
    
    public Sprite CardIcon 
    { 
        get { return cardIcon; } 
        set { cardIcon = value; }
    }
    
    public Color CardColor 
    { 
        get { return cardColor; } 
        set { cardColor = value; }
    }
    
    public CardType Type 
    { 
        get { return cardType; } 
        set { cardType = value; }
    }
    
    public CardRarity Rarity 
    { 
        get { return rarity; } 
        set { rarity = value; }
    }
    
    public int Cost 
    { 
        get { return cost; } 
        set { cost = Mathf.Max(0, value); }
    }
    
    public bool IsPlayable 
    { 
        get { return isPlayable; } 
        set { isPlayable = value; }
    }
    
    public int Value 
    { 
        get { return value; } 
        set { this.value = value; }
    }
    
    public int Durability 
    { 
        get { return durability; } 
        set { durability = Mathf.Max(1, value); }
    }
    
    public bool IsConsumable 
    { 
        get { return isConsumable; } 
        set { isConsumable = value; }
    }

    /// <summary>
    /// Kartın oynanabilir olup olmadığını kontrol eder
    /// </summary>
    /// <param name="availableResources">Mevcut kaynaklar</param>
    /// <returns>Oynanabilir mi</returns>
    public virtual bool CanPlay(int availableResources = 0)
    {
        return isPlayable && availableResources >= cost;
    }

    /// <summary>
    /// Kart oynanırken çağrılır
    /// </summary>
    public virtual void OnPlay()
    {
        // Override edilecek
    }

    /// <summary>
    /// Kart kullanıldığında çağrılır
    /// </summary>
    public virtual void OnUse()
    {
        if (isConsumable)
        {
            // Tüketilebilir kartlar için özel işlem
        }
    }

    /// <summary>
    /// Kartın rarity'sine göre renk döndürür
    /// </summary>
    /// <returns>Rarity rengi</returns>
    public Color GetRarityColor()
    {
        switch (rarity)
        {
            case CardRarity.Common:
                return Color.gray;
            case CardRarity.Uncommon:
                return Color.green;
            case CardRarity.Rare:
                return Color.blue;
            case CardRarity.Epic:
                return Color.magenta;
            case CardRarity.Legendary:
                return Color.yellow;
            default:
                return Color.white;
        }
    }

    /// <summary>
    /// Kartın türüne göre Türkçe isim döndürür
    /// </summary>
    /// <returns>Tür adı</returns>
    public string GetTypeString()
    {
        switch (cardType)
        {
            case CardType.Crop:
                return "Ekin";
            case CardType.Tool:
                return "Araç";
            case CardType.Action:
                return "Aksiyon";
            case CardType.Special:
                return "Özel";
            case CardType.Resource:
                return "Kaynak";
            default:
                return "Bilinmeyen";
        }
    }

    /// <summary>
    /// Kartın rarity'sine göre Türkçe isim döndürür
    /// </summary>
    /// <returns>Rarity adı</returns>
    public string GetRarityString()
    {
        switch (rarity)
        {
            case CardRarity.Common:
                return "Yaygın";
            case CardRarity.Uncommon:
                return "Nadir";
            case CardRarity.Rare:
                return "Ender";
            case CardRarity.Epic:
                return "Epik";
            case CardRarity.Legendary:
                return "Efsanevi";
            default:
                return "Bilinmeyen";
        }
    }

    /// <summary>
    /// Kartın tam bilgilerini string olarak döndürür
    /// </summary>
    /// <returns>Kart bilgileri</returns>
    public override string ToString()
    {
        return $"{cardName} ({GetTypeString()}) - {GetRarityString()} - Maliyet: {cost}";
    }

    /// <summary>
    /// Kartı kopyalar (deep copy)
    /// </summary>
    /// <returns>Kopya kart</returns>
    public virtual BaseCard Clone()
    {
        BaseCard clone = CreateInstance<BaseCard>();
        clone.cardName = this.cardName;
        clone.cardID = this.cardID;
        clone.description = this.description;
        clone.cardImage = this.cardImage;
        clone.cardIcon = this.cardIcon;
        clone.cardColor = this.cardColor;
        clone.cardType = this.cardType;
        clone.rarity = this.rarity;
        clone.cost = this.cost;
        clone.isPlayable = this.isPlayable;
        clone.value = this.value;
        clone.durability = this.durability;
        clone.isConsumable = this.isConsumable;
        return clone;
    }

    // Validation
    private void OnValidate()
    {
        // ID boşsa otomatik oluştur
        if (string.IsNullOrEmpty(cardID))
        {
            cardID = name.Replace(" ", "_").ToLower();
        }
        
        // Negatif değerleri düzelt
        cost = Mathf.Max(0, cost);
        durability = Mathf.Max(1, durability);
    }
}

using UnityEngine;
using UnityEngine.Events;

public class GameManager : MonoBehaviour
{
    [Header("Game State")]
    [SerializeField] private Season currentSeason = Season.Spring;

    [Header("Events")]
    public UnityEvent<Season> OnSeasonChanged;

    // Singleton pattern
    public static GameManager Instance { get; private set; }

    // Properties
    public Season CurrentSeason
    {
        get { return currentSeason; }
        private set
        {
            if (currentSeason != value)
            {
                currentSeason = value;
                OnSeasonChanged?.Invoke(currentSeason);
            }
        }
    }

    private void Awake()
    {
        // Singleton setup
        if (Instance == null)
        {
            Instance = this;
            DontDestroyOnLoad(gameObject);
        }
        else
        {
            Destroy(gameObject);
        }
    }

    private void Start()
    {
        // İlk mevsimi ayarla
        OnSeasonChanged?.Invoke(currentSeason);
    }

    /// <summary>
    /// Sonraki mevsime geçer
    /// </summary>
    public void NextSeason()
    {
        CurrentSeason = Seasons.GetNextSeason(currentSeason);
        Debug.Log($"Mevsim değişti: {Seasons.GetSeasonString(currentSeason)}");
    }

    /// <summary>
    /// Belirtilen mevsime geçer
    /// </summary>
    /// <param name="newSeason">Yeni mevsim</param>
    public void SetSeason(Season newSeason)
    {
        CurrentSeason = newSeason;
        Debug.Log($"Mevsim ayarlandı: {Seasons.GetSeasonString(currentSeason)}");
    }

    /// <summary>
    /// Mevcut mevsimin string halini döndürür
    /// </summary>
    /// <returns>Mevsim adı</returns>
    public string GetCurrentSeasonString()
    {
        return Seasons.GetSeasonString(currentSeason);
    }

    /// <summary>
    /// Mevcut mevsimin rengini döndürür
    /// </summary>
    /// <returns>Mevsim rengi</returns>
    public Color GetCurrentSeasonColor()
    {
        return Seasons.GetSeasonColor(currentSeason);
    }
}

using UnityEngine;
using UnityEngine.Events;
using System.Collections;

public class GameManager : MonoBehaviour
{
    [Header("Game State")]
    [SerializeField] private Season currentSeason = Season.Spring;

    [Header("Season Transition")]
    [SerializeField] private float seasonTransitionDuration = 10f;
    [SerializeField] private bool isTransitioning = false;

    [Header("Events")]
    public UnityEvent<Season> OnSeasonChanged;
    public UnityEvent<bool> OnSeasonTransitionStateChanged;

    // Singleton pattern
    public static GameManager Instance { get; private set; }

    // Properties
    public Season CurrentSeason
    {
        get { return currentSeason; }
        private set
        {
            if (currentSeason != value)
            {
                currentSeason = value;
                OnSeasonChanged?.Invoke(currentSeason);
            }
        }
    }

    public bool IsTransitioning
    {
        get { return isTransitioning; }
        private set
        {
            if (isTransitioning != value)
            {
                isTransitioning = value;
                OnSeasonTransitionStateChanged?.Invoke(isTransitioning);
            }
        }
    }

    public float SeasonTransitionDuration
    {
        get { return seasonTransitionDuration; }
    }

    private void Awake()
    {
        // Singleton setup
        if (Instance == null)
        {
            Instance = this;
            // Root GameObject olduğundan emin ol
            if (transform.parent != null)
            {
                transform.SetParent(null);
            }
            DontDestroyOnLoad(gameObject);
        }
        else
        {
            Destroy(gameObject);
        }
    }

    private void Start()
    {
        // İlk mevsimi ayarla
        OnSeasonChanged?.Invoke(currentSeason);
    }

    /// <summary>
    /// Sonraki mevsime geçer (animasyonlu)
    /// </summary>
    public void NextSeason()
    {
        if (!isTransitioning)
        {
            StartCoroutine(SeasonTransitionCoroutine());
        }
        else
        {
            Debug.Log("Mevsim geçişi zaten devam ediyor!");
        }
    }

    /// <summary>
    /// Mevsim geçiş coroutine'i
    /// </summary>
    /// <returns></returns>
    private IEnumerator SeasonTransitionCoroutine()
    {
        IsTransitioning = true;
        Season nextSeason = Seasons.GetNextSeason(currentSeason);

        Debug.Log($"Mevsim geçişi başladı: {Seasons.GetSeasonString(currentSeason)} → {Seasons.GetSeasonString(nextSeason)}");

        // 10 saniye bekle
        yield return new WaitForSeconds(seasonTransitionDuration);

        // Mevsimi değiştir
        CurrentSeason = nextSeason;
        IsTransitioning = false;

        Debug.Log($"Mevsim geçişi tamamlandı: {Seasons.GetSeasonString(currentSeason)}");
    }

    /// <summary>
    /// Belirtilen mevsime geçer
    /// </summary>
    /// <param name="newSeason">Yeni mevsim</param>
    public void SetSeason(Season newSeason)
    {
        CurrentSeason = newSeason;
        Debug.Log($"Mevsim ayarlandı: {Seasons.GetSeasonString(currentSeason)}");
    }

    /// <summary>
    /// Mevcut mevsimin string halini döndürür
    /// </summary>
    /// <returns>Mevsim adı</returns>
    public string GetCurrentSeasonString()
    {
        return Seasons.GetSeasonString(currentSeason);
    }

    /// <summary>
    /// Mevcut mevsimin rengini döndürür
    /// </summary>
    /// <returns>Mevsim rengi</returns>
    public Color GetCurrentSeasonColor()
    {
        return Seasons.GetSeasonColor(currentSeason);
    }
}

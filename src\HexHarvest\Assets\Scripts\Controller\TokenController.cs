using System.Collections.Generic;
using UnityEngine;

public class TokenController : MonoBeh<PERSON><PERSON>
{
    [Header("Card Settings")]
    [SerializeField] private Transform cardContainer; // Kullanılan kartların yerleştirileceği container
    [SerializeField] private string cardsResourcePath = "Cards";
    [SerializeField] private int maxVisibleCards = 5;

    [Header("Debug")]
    [SerializeField] private bool showDebugInfo = false;

    // Card data
    private List<CardSO> allCards = new List<CardSO>();
    private List<CardSO> currentSeasonCards = new List<CardSO>();
    private List<CardUI> displayedCards = new List<CardUI>();

    // Singleton pattern
    public static TokenController Instance { get; private set; }

    // Properties
    public int TotalCardsCount => allCards.Count;
    public int CurrentSeasonCardsCount => currentSeasonCards.Count;
    public int DisplayedCardsCount => displayedCards.Count;

    private void Awake()
    {
        // Singleton setup
        if (Instance == null)
        {
            Instance = this;
        }
        else
        {
            Destroy(gameObject);
            return;
        }
    }

    private void Start()
    {
        // Card container'ı oluştur (eğer atanmamışsa)
        if (cardContainer == null)
        {
            GameObject containerObject = new GameObject("ActiveCards_Container");
            containerObject.transform.SetParent(transform);
            cardContainer = containerObject.transform;
        }

        LoadAllCards();

        // GameManager event'ini dinle
        if (GameManager.Instance != null)
        {
            GameManager.Instance.OnSeasonChanged.AddListener(OnSeasonChanged);
            // İlk güncelleme
            OnSeasonChanged(GameManager.Instance.CurrentSeason);
        }
        else
        {
            Debug.LogWarning("TokenController: GameManager bulunamadı!");
        }
    }

    private void OnDestroy()
    {
        // Event'leri temizle
        if (GameManager.Instance != null)
        {
            GameManager.Instance.OnSeasonChanged.RemoveListener(OnSeasonChanged);
        }
    }

    /// <summary>
    /// Resources klasöründen tüm kartları yükler
    /// </summary>
    private void LoadAllCards()
    {
        allCards.Clear();

        // Resources'dan tüm CardSO'ları yükle
        CardSO[] loadedCards = Resources.LoadAll<CardSO>(cardsResourcePath);

        if (loadedCards.Length == 0)
        {
            Debug.LogWarning($"TokenController: '{cardsResourcePath}' klasöründe kart bulunamadı!");
            return;
        }

        allCards.AddRange(loadedCards);

        if (showDebugInfo)
        {
            Debug.Log($"TokenController: {allCards.Count} kart yüklendi");
            foreach (CardSO card in allCards)
            {
                Debug.Log($"- {card.CardName} ({card.GetSeasonString()})");
            }
        }
    }

    /// <summary>
    /// Mevsim değiştiğinde çağrılır
    /// </summary>
    /// <param name="newSeason">Yeni mevsim</param>
    private void OnSeasonChanged(Season newSeason)
    {
        UpdateCurrentSeasonCards(newSeason);
        RefreshDisplayedCards();

        if (showDebugInfo)
        {
            Debug.Log($"TokenController: Mevsim değişti - {Seasons.GetSeasonString(newSeason)}. " +
                     $"Bu mevsime ait {currentSeasonCards.Count} kart bulundu.");
        }
    }

    /// <summary>
    /// Mevcut mevsime ait kartları filtreler
    /// </summary>
    /// <param name="season">Mevsim</param>
    private void UpdateCurrentSeasonCards(Season season)
    {
        currentSeasonCards.Clear();

        foreach (CardSO card in allCards)
        {
            if (card.CardSeason == season)
            {
                currentSeasonCards.Add(card);
            }
        }
    }

    /// <summary>
    /// Görüntülenen kartları yeniler
    /// </summary>
    private void RefreshDisplayedCards()
    {
        // Mevcut kartları pool'a döndür
        ReturnAllDisplayedCards();

        // Yeni kartları göster
        DisplayCards();
    }

    /// <summary>
    /// Kartları görüntüler
    /// </summary>
    private void DisplayCards()
    {
        if (CardPool.Instance == null)
        {
            Debug.LogError("TokenController: CardPool bulunamadı!");
            return;
        }

        int cardsToShow = Mathf.Min(maxVisibleCards, currentSeasonCards.Count);

        for (int i = 0; i < cardsToShow; i++)
        {
            CardUI cardUI = CardPool.Instance.GetCard(cardContainer);
            if (cardUI != null)
            {
                cardUI.SetCard(currentSeasonCards[i]);
                displayedCards.Add(cardUI);
            }
        }

        if (showDebugInfo)
        {
            Debug.Log($"TokenController: {cardsToShow} kart görüntülendi");
        }
    }

    /// <summary>
    /// Tüm görüntülenen kartları pool'a döndürür
    /// </summary>
    private void ReturnAllDisplayedCards()
    {
        if (CardPool.Instance == null) return;

        foreach (CardUI cardUI in displayedCards)
        {
            if (cardUI != null)
            {
                CardPool.Instance.ReturnCard(cardUI);
            }
        }

        displayedCards.Clear();
    }

    /// <summary>
    /// Kartları yeniden yükler
    /// </summary>
    public void ReloadCards()
    {
        LoadAllCards();

        if (GameManager.Instance != null)
        {
            OnSeasonChanged(GameManager.Instance.CurrentSeason);
        }
    }

    /// <summary>
    /// Maksimum görüntülenecek kart sayısını ayarlar
    /// </summary>
    /// <param name="maxCards">Maksimum kart sayısı</param>
    public void SetMaxVisibleCards(int maxCards)
    {
        maxVisibleCards = Mathf.Max(1, maxCards);
        RefreshDisplayedCards();
    }

    /// <summary>
    /// Controller istatistiklerini döndürür
    /// </summary>
    /// <returns>İstatistik bilgileri</returns>
    public string GetStats()
    {
        return $"TokenController - Toplam Kart: {TotalCardsCount}, " +
               $"Mevcut Mevsim: {CurrentSeasonCardsCount}, " +
               $"Görüntülenen: {DisplayedCardsCount}";
    }

    // Debug metodları
    [ContextMenu("Debug - Reload Cards")]
    private void DebugReloadCards()
    {
        ReloadCards();
    }

    [ContextMenu("Debug - Show Stats")]
    private void DebugShowStats()
    {
        Debug.Log(GetStats());
        if (CardPool.Instance != null)
        {
            Debug.Log(CardPool.Instance.GetPoolStats());
        }
    }

    [ContextMenu("Debug - Refresh Display")]
    private void DebugRefreshDisplay()
    {
        RefreshDisplayedCards();
    }
}

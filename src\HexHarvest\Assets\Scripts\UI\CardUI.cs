using UnityEngine;
using UnityEngine.UI;
using TMPro;

public class CardUI : MonoBehaviour
{
    [Header("UI References")]
    [SerializeField] private Image cardIcon;
    [SerializeField] private TextMeshProUGUI cardNameText;
    
    [Header("Card Data")]
    [SerializeField] private CardSO currentCard;
    
    // Properties
    public CardSO CurrentCard 
    { 
        get { return currentCard; } 
        private set { currentCard = value; }
    }
    
    public bool IsActive 
    { 
        get { return gameObject.activeSelf; } 
    }

    /// <summary>
    /// Kartı ayarlar ve UI'ı günceller
    /// </summary>
    /// <param name="cardData">Kart verisi</param>
    public void SetCard(CardSO cardData)
    {
        if (cardData == null)
        {
            Debug.LogWarning("CardUI: Null card data!");
            return;
        }

        currentCard = cardData;
        UpdateUI();
    }

    /// <summary>
    /// UI'ı günceller
    /// </summary>
    private void UpdateUI()
    {
        if (currentCard == null) return;

        // Kart ikonu
        if (cardIcon != null && currentCard.CardSprite != null)
        {
            cardIcon.sprite = currentCard.CardSprite;
            cardIcon.color = Color.white;
        }

        // Kart ismi
        if (cardNameText != null)
        {
            cardNameText.text = currentCard.CardName;
        }
    }

    /// <summary>
    /// Kartı temizler
    /// </summary>
    public void ClearCard()
    {
        currentCard = null;

        if (cardIcon != null)
        {
            cardIcon.sprite = null;
            cardIcon.color = Color.clear;
        }

        if (cardNameText != null)
        {
            cardNameText.text = "";
        }
    }

    /// <summary>
    /// Kartı aktif/pasif yapar
    /// </summary>
    /// <param name="active">Aktif mi</param>
    public void SetActive(bool active)
    {
        gameObject.SetActive(active);
    }

    /// <summary>
    /// Kartın mevcut mevsimle uyumlu olup olmadığını kontrol eder
    /// </summary>
    /// <param name="currentSeason">Mevcut mevsim</param>
    /// <returns>Uyumlu mu</returns>
    public bool IsCompatibleWithSeason(Season currentSeason)
    {
        return currentCard != null && currentCard.CardSeason == currentSeason;
    }

    /// <summary>
    /// Kartı pool'a döndürmeye hazırlar
    /// </summary>
    public void PrepareForPool()
    {
        ClearCard();
        SetActive(false);
        transform.SetParent(null);
    }

    /// <summary>
    /// Kartı pool'dan çıkarıp kullanıma hazırlar
    /// </summary>
    /// <param name="parent">Yeni parent transform</param>
    public void PrepareForUse(Transform parent = null)
    {
        if (parent != null)
        {
            transform.SetParent(parent);
        }
        
        SetActive(true);
    }

    // Debug için
    private void OnValidate()
    {
        if (Application.isPlaying && currentCard != null)
        {
            UpdateUI();
        }
    }
}

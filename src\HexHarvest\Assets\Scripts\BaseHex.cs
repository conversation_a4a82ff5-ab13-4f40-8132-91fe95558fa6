using UnityEngine;

public class BaseHex : MonoBehaviour
{
    [System.Serializable]
    public enum HexState
    {
        Empty,      // Boş durum
        Full,       // Dolu durum  
        Destroyed   // Yıkılmış durum
    }

    [Header("Hex Settings")]
    [SerializeField] private HexState currentState = HexState.Empty;
    
    [Header("Y Position Values")]
    [SerializeField] private float emptyYPosition = -0.73f;
    [SerializeField] private float fullYPosition = -0.462f;
    [SerializeField] private float destroyedYPosition = -1.556f;
    
    [Header("Animation Settings")]
    [SerializeField] private float transitionSpeed = 2f;
    [SerializeField] private bool useAnimation = true;
    
    private Vector3 targetPosition;
    private bool isTransitioning = false;

    // Properties
    public HexState CurrentState 
    { 
        get { return currentState; } 
        private set { currentState = value; }
    }

    public bool IsTransitioning 
    { 
        get { return isTransitioning; } 
    }

    private void Start()
    {
        // Başlangıçta mevcut state'e göre pozisyonu ayarla
        SetPositionForState(currentState, false);
    }

    private void Update()
    {
        // Animasyon aktifse pozisyon geçişini yap
        if (isTransitioning && useAnimation)
        {
            transform.position = Vector3.MoveTowards(transform.position, targetPosition, transitionSpeed * Time.deltaTime);
            
            if (Vector3.Distance(transform.position, targetPosition) < 0.01f)
            {
                transform.position = targetPosition;
                isTransitioning = false;
            }
        }
    }

    /// <summary>
    /// Hex'in state'ini değiştirir
    /// </summary>
    /// <param name="newState">Yeni state</param>
    /// <param name="animated">Animasyonlu geçiş yapılsın mı</param>
    public void ChangeState(HexState newState, bool animated = true)
    {
        if (currentState == newState) return;

        currentState = newState;
        SetPositionForState(newState, animated && useAnimation);
    }

    /// <summary>
    /// Hex'i boş duruma getirir
    /// </summary>
    /// <param name="animated">Animasyonlu geçiş yapılsın mı</param>
    public void SetEmpty(bool animated = true)
    {
        ChangeState(HexState.Empty, animated);
    }

    /// <summary>
    /// Hex'i dolu duruma getirir
    /// </summary>
    /// <param name="animated">Animasyonlu geçiş yapılsın mı</param>
    public void SetFull(bool animated = true)
    {
        ChangeState(HexState.Full, animated);
    }

    /// <summary>
    /// Hex'i yıkılmış duruma getirir
    /// </summary>
    /// <param name="animated">Animasyonlu geçiş yapılsın mı</param>
    public void SetDestroyed(bool animated = true)
    {
        ChangeState(HexState.Destroyed, animated);
    }

    /// <summary>
    /// Belirtilen state için pozisyonu ayarlar
    /// </summary>
    /// <param name="state">Hedef state</param>
    /// <param name="animated">Animasyonlu geçiş yapılsın mı</param>
    private void SetPositionForState(HexState state, bool animated)
    {
        float targetY = GetYPositionForState(state);
        targetPosition = new Vector3(transform.position.x, targetY, transform.position.z);

        if (animated && useAnimation)
        {
            isTransitioning = true;
        }
        else
        {
            transform.position = targetPosition;
            isTransitioning = false;
        }
    }

    /// <summary>
    /// Belirtilen state için Y pozisyonunu döndürür
    /// </summary>
    /// <param name="state">State</param>
    /// <returns>Y pozisyonu</returns>
    private float GetYPositionForState(HexState state)
    {
        switch (state)
        {
            case HexState.Empty:
                return emptyYPosition;
            case HexState.Full:
                return fullYPosition;
            case HexState.Destroyed:
                return destroyedYPosition;
            default:
                return emptyYPosition;
        }
    }

    /// <summary>
    /// Mevcut state'in string temsilini döndürür
    /// </summary>
    /// <returns>State adı</returns>
    public string GetStateString()
    {
        switch (currentState)
        {
            case HexState.Empty:
                return "Boş";
            case HexState.Full:
                return "Dolu";
            case HexState.Destroyed:
                return "Yıkılmış";
            default:
                return "Bilinmeyen";
        }
    }

    /// <summary>
    /// Inspector'da state değişikliği için test metodları
    /// </summary>
    [ContextMenu("Test - Set Empty")]
    private void TestSetEmpty()
    {
        SetEmpty();
    }

    [ContextMenu("Test - Set Full")]
    private void TestSetFull()
    {
        SetFull();
    }

    [ContextMenu("Test - Set Destroyed")]
    private void TestSetDestroyed()
    {
        SetDestroyed();
    }

    // Editor'da state değişikliklerini görmek için
    private void OnValidate()
    {
        if (Application.isPlaying) return;
        
        // Editor'da state değiştirildiğinde pozisyonu güncelle
        SetPositionForState(currentState, false);
    }
}

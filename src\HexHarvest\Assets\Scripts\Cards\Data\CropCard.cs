using UnityEngine;

[CreateAssetMenu(fileName = "New Crop Card", menuName = "HexHarvest/Cards/Crop Card", order = 2)]
public class CropCard : BaseCard
{
    [Header("Crop Specific")]
    [SerializeField] private int growthTime = 3;
    [SerializeField] private int harvestYield = 1;
    [SerializeField] private int waterRequirement = 1;
    [SerializeField] private bool needsFertilizer = false;
    [SerializeField] private CropType cropType = CropType.Grain;
    
    [Header("Growth Stages")]
    [SerializeField] private Sprite[] growthStageSprites;
    [SerializeField] private string[] growthStageNames;

    public enum CropType
    {
        Grain,      // Tahıl
        Vegetable,  // Sebze
        Fruit,      // Meyve
        Herb,       // Ot/Baharat
        Flower      // Çiçek
    }

    // Properties
    public int GrowthTime 
    { 
        get { return growthTime; } 
        set { growthTime = Mathf.Max(1, value); }
    }
    
    public int HarvestYield 
    { 
        get { return harvestYield; } 
        set { harvestYield = Mathf.Max(1, value); }
    }
    
    public int WaterRequirement 
    { 
        get { return waterRequirement; } 
        set { waterRequirement = Mathf.Max(1, value); }
    }
    
    public bool NeedsFertilizer 
    { 
        get { return needsFertilizer; } 
        set { needsFertilizer = value; }
    }
    
    public CropType Type 
    { 
        get { return cropType; } 
        set { cropType = value; }
    }
    
    public Sprite[] GrowthStageSprites 
    { 
        get { return growthStageSprites; } 
        set { growthStageSprites = value; }
    }

    /// <summary>
    /// Belirtilen büyüme aşaması için sprite döndürür
    /// </summary>
    /// <param name="stage">Büyüme aşaması (0-based)</param>
    /// <returns>Aşama sprite'ı</returns>
    public Sprite GetGrowthStageSprite(int stage)
    {
        if (growthStageSprites == null || growthStageSprites.Length == 0)
            return CardImage;
            
        stage = Mathf.Clamp(stage, 0, growthStageSprites.Length - 1);
        return growthStageSprites[stage] != null ? growthStageSprites[stage] : CardImage;
    }

    /// <summary>
    /// Belirtilen büyüme aşaması için isim döndürür
    /// </summary>
    /// <param name="stage">Büyüme aşaması (0-based)</param>
    /// <returns>Aşama ismi</returns>
    public string GetGrowthStageName(int stage)
    {
        if (growthStageNames == null || growthStageNames.Length == 0)
            return $"Aşama {stage + 1}";
            
        stage = Mathf.Clamp(stage, 0, growthStageNames.Length - 1);
        return !string.IsNullOrEmpty(growthStageNames[stage]) ? growthStageNames[stage] : $"Aşama {stage + 1}";
    }

    /// <summary>
    /// Ekin türüne göre Türkçe isim döndürür
    /// </summary>
    /// <returns>Ekin türü adı</returns>
    public string GetCropTypeString()
    {
        switch (cropType)
        {
            case CropType.Grain:
                return "Tahıl";
            case CropType.Vegetable:
                return "Sebze";
            case CropType.Fruit:
                return "Meyve";
            case CropType.Herb:
                return "Ot/Baharat";
            case CropType.Flower:
                return "Çiçek";
            default:
                return "Bilinmeyen";
        }
    }

    /// <summary>
    /// Ekin kartının oynanabilirliğini kontrol eder
    /// </summary>
    /// <param name="availableResources">Mevcut kaynaklar</param>
    /// <param name="availableWater">Mevcut su</param>
    /// <param name="hasFertilizer">Gübre var mı</param>
    /// <returns>Oynanabilir mi</returns>
    public bool CanPlant(int availableResources, int availableWater, bool hasFertilizer = false)
    {
        bool baseCanPlay = CanPlay(availableResources);
        bool hasEnoughWater = availableWater >= waterRequirement;
        bool fertilizerCheck = !needsFertilizer || hasFertilizer;
        
        return baseCanPlay && hasEnoughWater && fertilizerCheck;
    }

    /// <summary>
    /// Ekin kartı oynanırken çağrılır
    /// </summary>
    public override void OnPlay()
    {
        base.OnPlay();
        // Ekin ekme işlemleri burada yapılabilir
        Debug.Log($"{CardName} ekildi! Büyüme süresi: {growthTime} gün");
    }

    /// <summary>
    /// Hasat edildiğinde çağrılır
    /// </summary>
    /// <returns>Hasat miktarı</returns>
    public int OnHarvest()
    {
        Debug.Log($"{CardName} hasat edildi! Verim: {harvestYield}");
        return harvestYield;
    }

    /// <summary>
    /// Ekin kartını kopyalar
    /// </summary>
    /// <returns>Kopya kart</returns>
    public override BaseCard Clone()
    {
        CropCard clone = CreateInstance<CropCard>();
        
        // Base properties
        clone.CardName = this.CardName;
        clone.CardID = this.CardID;
        clone.Description = this.Description;
        clone.CardImage = this.CardImage;
        clone.CardIcon = this.CardIcon;
        clone.CardColor = this.CardColor;
        clone.Type = CardType.Crop;
        clone.Rarity = this.Rarity;
        clone.Cost = this.Cost;
        clone.IsPlayable = this.IsPlayable;
        clone.Value = this.Value;
        clone.Durability = this.Durability;
        clone.IsConsumable = this.IsConsumable;
        
        // Crop specific properties
        clone.growthTime = this.growthTime;
        clone.harvestYield = this.harvestYield;
        clone.waterRequirement = this.waterRequirement;
        clone.needsFertilizer = this.needsFertilizer;
        clone.cropType = this.cropType;
        clone.growthStageSprites = this.growthStageSprites;
        clone.growthStageNames = this.growthStageNames;
        
        return clone;
    }

    public override string ToString()
    {
        return $"{CardName} ({GetCropTypeString()}) - Büyüme: {growthTime} gün, Verim: {harvestYield}";
    }

    private void OnValidate()
    {
        base.OnValidate();
        
        // Ekin kartı için tip otomatik ayarla
        if (Type != CardType.Crop)
        {
            Type = CardType.Crop;
        }
        
        // Negatif değerleri düzelt
        growthTime = Mathf.Max(1, growthTime);
        harvestYield = Mathf.Max(1, harvestYield);
        waterRequirement = Mathf.Max(1, waterRequirement);
    }
}
